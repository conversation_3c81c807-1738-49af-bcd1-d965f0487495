// Product Model Management System
class ProductManager {
    constructor() {
        this.products = [];
        this.categories = ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Toys', 'Beauty', 'Automotive'];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filteredProducts = [];
        
        this.init();
    }

    init() {
        this.loadSampleData();
        this.populateCategories();
        this.renderProducts();
        this.updateStatistics();
        this.setupEventListeners();
        this.loadRecentActivity();
    }

    loadSampleData() {
        // Load sample products if no data exists
        if (this.products.length === 0) {
            this.products = [
                {
                    id: 1,
                    name: 'Smartphone Pro Max',
                    category: 'Electronics',
                    price: 999.99,
                    stock: 50,
                    status: 'active',
                    description: 'Latest flagship smartphone with advanced features',
                    tags: ['smartphone', 'electronics', 'mobile'],
                    createdAt: new Date('2024-01-15')
                },
                {
                    id: 2,
                    name: 'Wireless Headphones',
                    category: 'Electronics',
                    price: 199.99,
                    stock: 75,
                    status: 'active',
                    description: 'Premium noise-cancelling wireless headphones',
                    tags: ['headphones', 'audio', 'wireless'],
                    createdAt: new Date('2024-01-20')
                },
                {
                    id: 3,
                    name: 'Designer T-Shirt',
                    category: 'Clothing',
                    price: 29.99,
                    stock: 100,
                    status: 'active',
                    description: 'Comfortable cotton t-shirt with modern design',
                    tags: ['clothing', 'fashion', 'cotton'],
                    createdAt: new Date('2024-02-01')
                },
                {
                    id: 4,
                    name: 'Programming Guide',
                    category: 'Books',
                    price: 49.99,
                    stock: 25,
                    status: 'inactive',
                    description: 'Comprehensive guide to modern programming',
                    tags: ['books', 'programming', 'education'],
                    createdAt: new Date('2024-02-10')
                },
                {
                    id: 5,
                    name: 'Garden Tool Set',
                    category: 'Home & Garden',
                    price: 89.99,
                    stock: 30,
                    status: 'active',
                    description: 'Complete set of essential garden tools',
                    tags: ['garden', 'tools', 'outdoor'],
                    createdAt: new Date('2024-02-15')
                }
            ];
        }
        this.filteredProducts = [...this.products];
    }

    populateCategories() {
        const categorySelects = ['productCategory', 'editProductCategory', 'categoryFilter'];
        
        categorySelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                // Clear existing options (except first one for filters)
                if (selectId === 'categoryFilter') {
                    select.innerHTML = '<option value="">All Categories</option>';
                } else {
                    select.innerHTML = '<option value="">Select Category</option>';
                }
                
                this.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    select.appendChild(option);
                });
            }
        });
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.filterProducts());
        }

        // Filter functionality
        const categoryFilter = document.getElementById('categoryFilter');
        const statusFilter = document.getElementById('statusFilter');
        
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => this.filterProducts());
        }
        
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.filterProducts());
        }

        // Form submissions
        const addForm = document.getElementById('addProductForm');
        if (addForm) {
            addForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addProduct();
            });
        }
    }

    filterProducts() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const categoryFilter = document.getElementById('categoryFilter')?.value || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';

        this.filteredProducts = this.products.filter(product => {
            const matchesSearch = product.name.toLowerCase().includes(searchTerm) ||
                                product.description.toLowerCase().includes(searchTerm) ||
                                product.tags.some(tag => tag.toLowerCase().includes(searchTerm));
            
            const matchesCategory = !categoryFilter || product.category === categoryFilter;
            const matchesStatus = !statusFilter || product.status === statusFilter;

            return matchesSearch && matchesCategory && matchesStatus;
        });

        this.currentPage = 1;
        this.renderProducts();
    }

    renderProducts() {
        const tbody = document.getElementById('productsTableBody');
        if (!tbody) return;

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedProducts = this.filteredProducts.slice(startIndex, endIndex);

        tbody.innerHTML = '';

        if (paginatedProducts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-muted">
                        <i class="bi bi-inbox display-4 d-block mb-2"></i>
                        No products found
                    </td>
                </tr>
            `;
            return;
        }

        paginatedProducts.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-teal-20 text-primary-dark me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                            <i class="bi bi-box"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">${product.name}</h6>
                            <small class="text-muted text-truncate-2">${product.description}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-light text-dark">${product.category}</span>
                </td>
                <td class="fw-bold">$${product.price.toFixed(2)}</td>
                <td>
                    <span class="badge ${product.stock > 20 ? 'badge-success' : product.stock > 5 ? 'badge-warning' : 'badge-danger'}">
                        ${product.stock} units
                    </span>
                </td>
                <td>
                    <span class="badge ${product.status === 'active' ? 'badge-success' : 'badge-warning'}">
                        ${product.status.charAt(0).toUpperCase() + product.status.slice(1)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="productManager.editProduct(${product.id})" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="productManager.deleteProduct(${product.id})" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        this.renderPagination();
    }

    renderPagination() {
        const pagination = document.getElementById('pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="productManager.changePage(${this.currentPage - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="productManager.changePage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="productManager.changePage(${this.currentPage + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    changePage(page) {
        const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderProducts();
        }
    }

    updateStatistics() {
        const totalProducts = this.products.length;
        const activeProducts = this.products.filter(p => p.status === 'active').length;
        const totalCategories = [...new Set(this.products.map(p => p.category))].length;
        const totalValue = this.products.reduce((sum, p) => sum + (p.price * p.stock), 0);

        document.getElementById('totalProducts').textContent = totalProducts;
        document.getElementById('activeProducts').textContent = activeProducts;
        document.getElementById('totalCategories').textContent = totalCategories;
        document.getElementById('totalValue').textContent = `$${totalValue.toLocaleString()}`;
    }

    addProduct() {
        const form = document.getElementById('addProductForm');
        const formData = new FormData(form);
        
        const product = {
            id: Date.now(),
            name: document.getElementById('productName').value,
            category: document.getElementById('productCategory').value,
            price: parseFloat(document.getElementById('productPrice').value),
            stock: parseInt(document.getElementById('productStock').value),
            status: document.getElementById('productStatus').value,
            description: document.getElementById('productDescription').value,
            tags: document.getElementById('productTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
            createdAt: new Date()
        };

        this.products.push(product);
        this.filteredProducts = [...this.products];
        
        // Close modal and reset form
        const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
        modal.hide();
        form.reset();
        
        // Update UI
        this.renderProducts();
        this.updateStatistics();
        this.addActivity('Added new product: ' + product.name);
        
        this.showToast('Product added successfully!', 'success');
    }

    editProduct(id) {
        const product = this.products.find(p => p.id === id);
        if (!product) return;

        // Populate edit form
        document.getElementById('editProductId').value = product.id;
        document.getElementById('editProductName').value = product.name;
        document.getElementById('editProductCategory').value = product.category;
        document.getElementById('editProductPrice').value = product.price;
        document.getElementById('editProductStock').value = product.stock;
        document.getElementById('editProductStatus').value = product.status;
        document.getElementById('editProductDescription').value = product.description;
        document.getElementById('editProductTags').value = product.tags.join(', ');

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
        modal.show();
    }

    updateProduct() {
        const id = parseInt(document.getElementById('editProductId').value);
        const productIndex = this.products.findIndex(p => p.id === id);

        if (productIndex === -1) return;

        const updatedProduct = {
            ...this.products[productIndex],
            name: document.getElementById('editProductName').value,
            category: document.getElementById('editProductCategory').value,
            price: parseFloat(document.getElementById('editProductPrice').value),
            stock: parseInt(document.getElementById('editProductStock').value),
            status: document.getElementById('editProductStatus').value,
            description: document.getElementById('editProductDescription').value,
            tags: document.getElementById('editProductTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
            updatedAt: new Date()
        };

        this.products[productIndex] = updatedProduct;
        this.filteredProducts = [...this.products];

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
        modal.hide();

        // Update UI
        this.renderProducts();
        this.updateStatistics();
        this.addActivity('Updated product: ' + updatedProduct.name);

        this.showToast('Product updated successfully!', 'success');
    }

    deleteProduct(id) {
        const product = this.products.find(p => p.id === id);
        if (!product) return;

        if (confirm(`Are you sure you want to delete "${product.name}"?`)) {
            this.products = this.products.filter(p => p.id !== id);
            this.filteredProducts = [...this.products];

            this.renderProducts();
            this.updateStatistics();
            this.addActivity('Deleted product: ' + product.name);

            this.showToast('Product deleted successfully!', 'success');
        }
    }

    loadRecentActivity() {
        const activities = [
            { icon: 'plus-circle', text: 'Added new product: Smartphone Pro Max', time: '2 hours ago', type: 'success' },
            { icon: 'pencil', text: 'Updated product: Wireless Headphones', time: '4 hours ago', type: 'info' },
            { icon: 'trash', text: 'Deleted product: Old Model Phone', time: '1 day ago', type: 'danger' },
            { icon: 'upload', text: 'Imported 25 products from CSV', time: '2 days ago', type: 'info' },
            { icon: 'download', text: 'Exported product catalog', time: '3 days ago', type: 'secondary' }
        ];

        const container = document.getElementById('recentActivity');
        if (!container) return;

        container.innerHTML = activities.map(activity => `
            <div class="activity-item d-flex align-items-center">
                <div class="activity-icon bg-${activity.type === 'success' ? 'teal-20' : activity.type === 'danger' ? 'danger' : 'teal-10'}
                     text-${activity.type === 'danger' ? 'white' : 'primary-dark'} me-3">
                    <i class="bi bi-${activity.icon}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-medium">${activity.text}</div>
                    <small class="text-muted">${activity.time}</small>
                </div>
            </div>
        `).join('');
    }

    addActivity(text) {
        const container = document.getElementById('recentActivity');
        if (!container) return;

        const activityHTML = `
            <div class="activity-item d-flex align-items-center">
                <div class="activity-icon bg-teal-20 text-primary-dark me-3">
                    <i class="bi bi-clock"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-medium">${text}</div>
                    <small class="text-muted">Just now</small>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('afterbegin', activityHTML);

        // Keep only the latest 5 activities
        const activities = container.querySelectorAll('.activity-item');
        if (activities.length > 5) {
            activities[activities.length - 1].remove();
        }
    }

    showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast" role="alert">
                <div class="toast-header bg-${type === 'success' ? 'teal-20' : type === 'danger' ? 'danger' : 'primary'} text-${type === 'danger' ? 'white' : 'dark'}">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <strong class="me-auto">Notification</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// Quick Action Functions
function exportData() {
    const data = JSON.stringify(productManager.products, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'products-export.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    productManager.addActivity('Exported product data');
    productManager.showToast('Data exported successfully!', 'success');
}

function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedData = JSON.parse(e.target.result);
                if (Array.isArray(importedData)) {
                    productManager.products = importedData;
                    productManager.filteredProducts = [...productManager.products];
                    productManager.renderProducts();
                    productManager.updateStatistics();
                    productManager.addActivity(`Imported ${importedData.length} products`);
                    productManager.showToast('Data imported successfully!', 'success');
                } else {
                    throw new Error('Invalid data format');
                }
            } catch (error) {
                productManager.showToast('Error importing data: ' + error.message, 'danger');
            }
        };
        reader.readAsText(file);
    };

    input.click();
}

function generateReport() {
    const report = {
        generatedAt: new Date().toISOString(),
        totalProducts: productManager.products.length,
        activeProducts: productManager.products.filter(p => p.status === 'active').length,
        inactiveProducts: productManager.products.filter(p => p.status === 'inactive').length,
        categories: [...new Set(productManager.products.map(p => p.category))],
        totalValue: productManager.products.reduce((sum, p) => sum + (p.price * p.stock), 0),
        lowStockProducts: productManager.products.filter(p => p.stock < 10),
        products: productManager.products
    };

    const reportData = JSON.stringify(report, null, 2);
    const blob = new Blob([reportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'product-report.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    productManager.addActivity('Generated product report');
    productManager.showToast('Report generated successfully!', 'success');
}

function bulkUpdate() {
    productManager.showToast('Bulk update feature coming soon!', 'info');
}

// Initialize the application
let productManager;

document.addEventListener('DOMContentLoaded', function() {
    productManager = new ProductManager();
});
