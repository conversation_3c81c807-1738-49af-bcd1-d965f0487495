/* CSS Root Variables - Teal Color Palette */
:root {
    /* Teal Color Palette */
    --teal-10: #d1fafd;
    --teal-20: #74eef7;
    --teal-30: #34dce8;
    --teal-40: #2ec0cb;
    --teal-50: #1db6c0;
    --teal-60: #038d99;
    --teal-70: #007A87;
    --teal-80: #006075;
    --teal-90: #1d445d;
    --teal-100: #15384e;
    
    /* Primary Colors */
    --primary-light: var(--teal-10);
    --primary-medium: var(--teal-50);
    --primary-dark: var(--teal-90);
    
    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-muted: #95a5a6;
    
    /* Background Colors */
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    
    /* Shadow */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    /* Border Radius */
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    
    /* Transitions */
    --transition: all 0.3s ease;
}

/* Font Family */
* {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Body Styling */
body {
    background-color: var(--bg-light);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Custom Background Classes */
.bg-primary-light {
    background-color: var(--primary-light) !important;
}

.bg-primary-medium {
    background-color: var(--primary-medium) !important;
}

.bg-primary-dark {
    background-color: var(--primary-dark) !important;
}

.bg-gradient-teal {
    background: linear-gradient(135deg, var(--teal-60) 0%, var(--teal-80) 100%);
}

/* Teal Background Classes */
.bg-teal-10 { background-color: var(--teal-10) !important; }
.bg-teal-20 { background-color: var(--teal-20) !important; }
.bg-teal-30 { background-color: var(--teal-30) !important; }
.bg-teal-40 { background-color: var(--teal-40) !important; }
.bg-teal-50 { background-color: var(--teal-50) !important; }
.bg-teal-60 { background-color: var(--teal-60) !important; }
.bg-teal-70 { background-color: var(--teal-70) !important; }
.bg-teal-80 { background-color: var(--teal-80) !important; }
.bg-teal-90 { background-color: var(--teal-90) !important; }
.bg-teal-100 { background-color: var(--teal-100) !important; }

/* Text Color Classes */
.text-primary-light {
    color: var(--primary-light) !important;
}

.text-primary-medium {
    color: var(--primary-medium) !important;
}

.text-primary-dark {
    color: var(--primary-dark) !important;
}

/* Navigation Styling */
.navbar {
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    color: var(--text-primary) !important;
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-dark) !important;
}

/* Card Styling */
.card {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

/* Statistics Cards */
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

/* Button Styling */
.btn-primary {
    background-color: var(--primary-medium);
    border-color: var(--primary-medium);
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--primary-medium);
    border-color: var(--primary-medium);
    font-weight: 500;
    transition: var(--transition);
}

.btn-outline-primary:hover {
    background-color: var(--primary-medium);
    border-color: var(--primary-medium);
    transform: translateY(-1px);
}

/* Form Styling */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-medium);
    box-shadow: 0 0 0 0.2rem rgba(29, 182, 192, 0.25);
}

.input-group-text {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

/* Table Styling */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table-hover tbody tr:hover {
    background-color: var(--teal-10);
}

.table th {
    background-color: var(--bg-light);
    border-top: none;
    font-weight: 600;
    color: var(--text-primary);
}

/* Badge Styling */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

.badge-success {
    background-color: #28a745;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
}

/* Modal Styling */
.modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--shadow-md);
}

.modal-header {
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Pagination Styling */
.pagination .page-link {
    color: var(--primary-medium);
    border-color: #dee2e6;
    transition: var(--transition);
}

.pagination .page-link:hover {
    color: var(--primary-dark);
    background-color: var(--teal-10);
    border-color: var(--primary-medium);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-medium);
    border-color: var(--primary-medium);
}

/* Activity Item Styling */
.activity-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-number {
        font-size: 1.5rem;
    }
    
    .display-6 {
        font-size: 1.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(29, 182, 192, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-medium);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-start-primary {
    border-left: 4px solid var(--primary-medium) !important;
}
