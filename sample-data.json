{"products": [{"id": 1, "name": "Smartphone Pro Max", "category": "Electronics", "price": 999.99, "stock": 50, "status": "active", "description": "Latest flagship smartphone with advanced features including 5G connectivity, triple camera system, and all-day battery life", "tags": ["smartphone", "electronics", "mobile", "5G", "camera"], "createdAt": "2024-01-15T10:30:00Z"}, {"id": 2, "name": "Wireless Headphones", "category": "Electronics", "price": 199.99, "stock": 75, "status": "active", "description": "Premium noise-cancelling wireless headphones with 30-hour battery life and superior sound quality", "tags": ["headphones", "audio", "wireless", "noise-cancelling"], "createdAt": "2024-01-20T14:15:00Z"}, {"id": 3, "name": "Designer T-Shirt", "category": "Clothing", "price": 29.99, "stock": 100, "status": "active", "description": "Comfortable 100% organic cotton t-shirt with modern design and sustainable manufacturing", "tags": ["clothing", "fashion", "cotton", "organic", "sustainable"], "createdAt": "2024-02-01T09:45:00Z"}, {"id": 4, "name": "Programming Guide", "category": "Books", "price": 49.99, "stock": 25, "status": "inactive", "description": "Comprehensive guide to modern programming languages and best practices for developers", "tags": ["books", "programming", "education", "development"], "createdAt": "2024-02-10T16:20:00Z"}, {"id": 5, "name": "Garden Tool Set", "category": "Home & Garden", "price": 89.99, "stock": 30, "status": "active", "description": "Complete set of essential garden tools including spade, rake, pruners, and watering can", "tags": ["garden", "tools", "outdoor", "gardening"], "createdAt": "2024-02-15T11:30:00Z"}, {"id": 6, "name": "Fitness Tracker", "category": "Sports", "price": 149.99, "stock": 60, "status": "active", "description": "Advanced fitness tracker with heart rate monitoring, GPS, and 7-day battery life", "tags": ["fitness", "tracker", "health", "sports", "GPS"], "createdAt": "2024-02-20T13:45:00Z"}, {"id": 7, "name": "Educational Puzzle", "category": "Toys", "price": 24.99, "stock": 80, "status": "active", "description": "Interactive educational puzzle for children ages 3-8, promoting problem-solving skills", "tags": ["toys", "educational", "puzzle", "children", "learning"], "createdAt": "2024-02-25T08:15:00Z"}, {"id": 8, "name": "Skincare Set", "category": "Beauty", "price": 79.99, "stock": 40, "status": "active", "description": "Complete skincare routine set with cleanser, toner, serum, and moisturizer", "tags": ["beauty", "skincare", "cosmetics", "routine"], "createdAt": "2024-03-01T12:00:00Z"}, {"id": 9, "name": "Car Phone Mount", "category": "Automotive", "price": 19.99, "stock": 120, "status": "active", "description": "Universal car phone mount with 360-degree rotation and secure grip", "tags": ["automotive", "phone", "mount", "car", "accessories"], "createdAt": "2024-03-05T15:30:00Z"}, {"id": 10, "name": "Bluetooth Speaker", "category": "Electronics", "price": 79.99, "stock": 45, "status": "active", "description": "Portable Bluetooth speaker with waterproof design and 12-hour battery life", "tags": ["speaker", "bluetooth", "portable", "waterproof", "audio"], "createdAt": "2024-03-10T10:45:00Z"}], "categories": ["Electronics", "Clothing", "Books", "Home & Garden", "Sports", "Toys", "Beauty", "Automotive"], "metadata": {"version": "1.0", "lastUpdated": "2024-03-10T10:45:00Z", "totalProducts": 10, "activeProducts": 9, "inactiveProducts": 1}}