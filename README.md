# Product Model Management System

A professional and user-friendly Product Model GUI built with HTML, JavaScript, JSON, and Bootstrap. Features a modern teal color palette and HCL Tech Roboto font for a clean, professional appearance.

## Features

### 🎯 Core Functionality
- **Product Management**: Add, edit, delete, and view products
- **Search & Filter**: Real-time search with category and status filters
- **Pagination**: Efficient handling of large product catalogs
- **Statistics Dashboard**: Overview of total products, categories, and inventory value
- **Recent Activity**: Track all product management activities

### 🎨 Design Features
- **Professional UI/UX**: Clean, modern interface with teal color palette
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Bootstrap Integration**: Utilizes Bootstrap 5 for consistent styling
- **Custom Color Scheme**: Teal gradient from #d1fafd to #15384e
- **HCL Tech Roboto Font**: Professional typography throughout

### 📊 Data Management
- **JSON-based Storage**: All data stored in JSON format
- **Import/Export**: Easy data backup and migration
- **Sample Data**: Pre-loaded with sample products for demonstration
- **Report Generation**: Generate comprehensive product reports

## File Structure

```
Model/
├── Model.html          # Main HTML file
├── styles.css          # Custom CSS with teal color palette
├── script.js           # JavaScript functionality
├── sample-data.json    # Sample product data
└── README.md          # This documentation
```

## Color Palette

The application uses a professional teal color scheme:

- **Teal 10**: #d1fafd (Light backgrounds)
- **Teal 20**: #74eef7 (Accent elements)
- **Teal 30**: #34dce8 (Secondary elements)
- **Teal 40**: #2ec0cb (Interactive elements)
- **Teal 50**: #1db6c0 (Primary buttons)
- **Teal 60**: #038d99 (Primary backgrounds)
- **Teal 70**: #007A87 (Hover states)
- **Teal 80**: #006075 (Active states)
- **Teal 90**: #1d445d (Text and borders)
- **Teal 100**: #15384e (Dark text)

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No server setup required - runs entirely in the browser

### Installation
1. Download all files to a local directory
2. Open `Model.html` in your web browser
3. The application will load with sample data

### Usage

#### Adding Products
1. Click the "Add Product" button in the Product Management panel
2. Fill in the product details in the modal form
3. Click "Add Product" to save

#### Editing Products
1. Click the edit (pencil) icon next to any product in the table
2. Modify the product details in the modal form
3. Click "Update Product" to save changes

#### Searching and Filtering
- Use the search box to find products by name, description, or tags
- Filter by category using the category dropdown
- Filter by status (Active/Inactive) using the status dropdown

#### Quick Actions
- **Export Data**: Download all product data as JSON
- **Import Data**: Upload JSON file to replace current data
- **Generate Report**: Create a comprehensive product report
- **Bulk Update**: (Coming soon) Update multiple products at once

## Technical Details

### Technologies Used
- **HTML5**: Semantic markup and structure
- **CSS3**: Custom styling with CSS variables and flexbox/grid
- **JavaScript (ES6+)**: Modern JavaScript with classes and modules
- **Bootstrap 5**: Responsive framework and components
- **Bootstrap Icons**: Professional icon set
- **JSON**: Data storage and exchange format

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Performance Features
- **Pagination**: Handles large datasets efficiently
- **Lazy Loading**: Components load as needed
- **Responsive Images**: Optimized for different screen sizes
- **Minimal Dependencies**: Only Bootstrap and Google Fonts CDN

## Customization

### Changing Colors
All colors are defined as CSS variables in `styles.css`. Modify the `:root` section to change the color scheme:

```css
:root {
    --teal-10: #your-color;
    --teal-20: #your-color;
    /* ... etc */
}
```

### Adding New Categories
Categories are defined in the `ProductManager` class constructor in `script.js`:

```javascript
this.categories = ['Electronics', 'Clothing', 'Books', 'Your-Category'];
```

### Modifying Product Fields
To add new product fields:
1. Update the modal forms in `Model.html`
2. Modify the product object structure in `script.js`
3. Update the table rendering in the `renderProducts()` method

## Data Format

Products are stored in the following JSON format:

```json
{
  "id": 1,
  "name": "Product Name",
  "category": "Category",
  "price": 99.99,
  "stock": 50,
  "status": "active",
  "description": "Product description",
  "tags": ["tag1", "tag2"],
  "createdAt": "2024-01-15T10:30:00Z"
}
```

## Contributing

This is a standalone application designed for educational and demonstration purposes. Feel free to modify and extend it for your specific needs.

## License

This project is open source and available under the MIT License.

## Support

For questions or issues, please refer to the code comments or create an issue in the project repository.
